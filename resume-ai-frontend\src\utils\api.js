import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001/api/v1',
  timeout: 300000, // 5分钟超时 (300秒)
  headers: {
    'Content-Type': 'application/json',
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {

    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    } else {
    }

    // 如果是文件上传，删除Content-Type让浏览器自动设置
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type']
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    // console.error('🔍 前端DEBUG: 响应拦截器 - 错误响应:', {
    //   status: error.response?.status,
    //   statusText: error.response?.statusText,
    //   url: error.config?.url,
    //   message: error.message,
    //   responseData: error.response?.data
    // })

    // 处理常见错误
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('token')
          window.location.href = '/login'
          break
        case 403:
          break
        case 404:
          break
        case 500:
          break
        default:
      }
    } else if (error.request) {
    } else {
    }

    return Promise.reject(error)
  }
)

// API接口定义 - 重新组织以支持三页面结构
export const resumeApi = {
  // 📂 简历管理页面相关API

  // 上传简历文件
  uploadResume: (file, title) => {

    const formData = new FormData()
    formData.append('file', file)
    if (title) formData.append('title', title)


    return api.post('/resume/upload', formData)
  },

  // 获取简历列表 - 使用原始路径保持一致性
  getResumeList: (skip = 0, limit = 100) => {
    return api.get(`/resume/?skip=${skip}&limit=${limit}`)
  },

  // 删除简历
  deleteResume: (resumeId) => {
    return api.delete(`/resume/${resumeId}`)
  },

  // 创建空白简历 - 使用新的简化接口
  createBlankResume: () => {
    return api.post('/resume/', {
      title: `空白简历_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}`,
      structured_data: {
        name: "",
        email: "",
        phone: "",
        location: "",
        summary: "",
        experience: [],
        skills: [],
        education: [],
        projects: []
      }
    })
  },

  // 简历预览
  getResumePreview: (resumeId) => {
    return api.get(`/resume/${resumeId}`)
  },

  // ✍️ 简历修改页面相关API

  // 获取简历详细数据
  getResume: (resumeId) => {
    return api.get(`/resume/${resumeId}`)
  },

  // 保存简历
  updateResume: (resumeId, resumeData) => {
    return api.put(`/resume/${resumeId}`, resumeData)
  },

  // 导出简历 - 使用简化接口
  exportResume: (resumeId, format = 'pdf') => {
    return api.post(`/resume/${resumeId}/export`, { format }, {
      responseType: 'blob'
    })
  },

  // 版本管理
  getResumeVersions: (resumeId) => {
    return api.get(`/resume/${resumeId}/versions`)
  },

  // 解析简历文本（兼容旧接口）
  parseResume: (resumeText) => {
    return api.post('/resume/parse-text', { resume_text: resumeText })
  }
}

export const jdApi = {
  // ✍️ 简历修改页面 - JD解析相关API

  // 解析JD文本
  parseJD: (jdText, title, companyName) => {
    return api.post('/parse_jd/', {
      jd_text: jdText,
      title: title,
      company_name: companyName
    })
  },

  // 创建并解析JD（兼容旧接口）
  createAndParseJD: (jdText, title, companyName) => {
    return api.post('/job-description/create-and-parse', {
      jd_text: jdText,
      title: title,
      company_name: companyName
    })
  }
}

export const matchApi = {
  // ✍️ 简历修改页面 - 匹配分析相关API

  // 简历与JD匹配分析
  analyzeMatch: (data) => {
    return api.post('/match_resume/', data)
  },

  // 生成优化建议
  generateOptimization: (data) => {
    return api.post('/generate_optimization/', data)
  },

  // 获取匹配结果详情（兼容旧接口）
  getMatchResult: (matchId) => {
    return api.get(`/match/${matchId}`)
  },

  // 获取优化建议（兼容旧接口）
  getOptimizationSuggestions: (matchId) => {
    return api.get(`/match/${matchId}/suggestions`)
  }
}

export const exportApi = {
  // 导出简历为PDF
  exportToPDF: (resumeData, templateId = 'default') => {
    return api.post('/export/pdf/', {
      resume_data: resumeData,
      template_id: templateId
    }, {
      responseType: 'blob'
    })
  },

  // 导出简历为Word
  exportToWord: (resumeData, templateId = 'default') => {
    return api.post('/export/word/', {
      resume_data: resumeData,
      template_id: templateId
    }, {
      responseType: 'blob'
    })
  }
}

export const authApi = {
  // 🏠 首页相关API - 用户认证

  // 用户登录
  login: (email, password) => {
    return api.post('/auth/login/', { email, password })
  },

  // 用户注册
  register: (username, email, password) => {
    return api.post('/auth/register/', { username, email, password })
  },

  // 获取用户信息
  getUserInfo: () => {
    return api.get('/auth/me/')
  },

  // 刷新token
  refreshToken: (refreshToken) => {
    return api.post('/auth/refresh/', { refresh_token: refreshToken })
  },

  // 登出
  logout: () => {
    return api.post('/auth/logout/')
  },

  // 请求密码重置
  requestPasswordReset: (email) => {
    return api.post('/auth/password-reset/', { email })
  },

  // 确认密码重置
  confirmPasswordReset: (token, newPassword) => {
    return api.post('/auth/password-reset/confirm/', {
      token,
      new_password: newPassword
    })
  }
}

export const userApi = {
  // 获取用户使用统计
  getUsageStats: () => {
    return api.get('/user/stats/')
  },

  // 获取用户简历历史
  getResumeHistory: () => {
    return api.get('/user/resumes/')
  }
}

// 兼容性导出 - 保持向后兼容
export const resumeAPI = resumeApi
export const jdAPI = jdApi
export const matchAPI = matchApi
export const exportAPI = exportApi
export const authAPI = authApi
export const userAPI = userApi

// 默认导出api实例，用于自定义请求
export default api
