#!/usr/bin/env python3
"""
测试上传接口
"""

import requests
import os

def test_upload():
    """测试上传接口"""
    
    # API端点
    url = "http://localhost:8001/api/v1/resume/upload"
    
    # 认证token（从之前的日志中获取）
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzIiwiZXhwIjoxNzIyNDI2NjM0fQ.Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7E"
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    # 创建一个测试文件
    test_file_content = b"Test PDF content"
    
    # 准备文件数据
    files = {
        'file': ('test.pdf', test_file_content, 'application/pdf')
    }
    
    data = {
        'title': 'Test Resume'
    }
    
    print("🔍 测试上传接口...")
    print(f"URL: {url}")
    print(f"Headers: {headers}")
    print(f"Files: {files}")
    print(f"Data: {data}")
    
    try:
        response = requests.post(url, files=files, data=data, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {response.headers}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 422:
            print("❌ 422错误 - 参数验证失败")
            try:
                error_detail = response.json()
                print(f"错误详情: {error_detail}")
            except:
                print("无法解析错误详情")
        elif response.status_code == 200:
            print("✅ 上传成功")
        else:
            print(f"❌ 其他错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_upload()
