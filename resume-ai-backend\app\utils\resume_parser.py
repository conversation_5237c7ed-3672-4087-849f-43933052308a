"""
简历解析器 - 基于demo的成功经验重构

功能:
1. 解析个人信息
2. 解析工作经历
3. 解析项目经验
4. 解析教育背景
5. 清理特殊符号
"""

import re
from typing import Dict, List, Any


class ResumeParser:
    """简历解析器"""
    
    @staticmethod
    def clean_special_symbols(text: str) -> str:
        """清理特殊符号的通用函数"""
        if not text:
            return text
        
        # 处理 ${数字+} 格式
        text = re.sub(r'\$\{\s*(\d+)\s*\+\s*\}', r'\1+', text)  # ${20+} -> 20+
        text = re.sub(r'\$\{\s*(\d+)\s*(\d+)\s*\+\s*\}', r'\1\2+', text)  # ${2 0+} -> 20+
        
        # 处理 ${数字} 格式
        text = re.sub(r'\$\{\s*(\d+)\s*(\d+)\s*\}', r'\1\2', text)  # ${1 5} -> 15
        text = re.sub(r'\$\{\s*(\d+)\s*\}', r'\1', text)  # ${15} -> 15
        
        # 处理 $+$ 格式
        text = re.sub(r'\$\+\$', '+', text)  # $+$ -> +
        
        # 处理百分比格式
        text = re.sub(r'\$\s*(\d+)\s*\\\s*%\s*\$', r'\1%', text)  # $4 5 \%$ -> 45%
        text = re.sub(r'\$\s*(\d+)\s*(\d+)\s*\\\s*%\s*\$', r'\1\2%', text)  # $2 8 \%$ -> 28%
        text = re.sub(r'\$\s*(\d+)\s*(\d+)\s*\s*\\\s*%\s*\$', r'\1\2%', text)  # $3 0 \%$ -> 30%
        
        # 处理双反斜杠格式
        text = re.sub(r'\$\s*(\d+)\s*(\d+)\s*\\\\\s*%\s*\$', r'\1\2%', text)  # $4 5 \\%$ -> 45%
        text = re.sub(r'\$\s*(\d+)\s*\\\\\s*%\s*\$', r'\1%', text)  # $5 \\%$ -> 5%
        text = re.sub(r'\$\s*(\d+)\s*(\d+)\s*\s*\\\\\s*%\s*\$', r'\1\2%', text)  # $5 0 \\%$ -> 50%
        
        # 处理不带反斜杠的格式
        text = re.sub(r'\$\s*(\d+)\s*%\s*\$', r'\1%', text)  # $45 %$ -> 45%
        text = re.sub(r'\$\s*(\d+)\s*(\d+)\s*%\s*\$', r'\1\2%', text)  # $28 %$ -> 28%
        
        # 处理万+格式
        text = re.sub(r'(\d+)万\s*\$\+\$', r'\1万+', text)  # 10万 $+$ -> 10万+
        
        # 处理数字+$格式
        text = re.sub(r'(\d+)\+\$', r'\1+', text)  # 20+$ -> 20+
        
        return text.strip()
    
    @staticmethod
    def parse_personal_info(original_text: str) -> Dict[str, str]:
        """解析个人信息"""
        personal_info = {}

        # 提取姓名 - 查找 # 开头的第一行
        name_match = re.search(r'^#\s*(.+)$', original_text, re.MULTILINE)
        if name_match:
            personal_info["姓名"] = name_match.group(1).strip()

        # 提取电话
        phone_patterns = [
            r'手机[：:]\s*(\d{3}-\d{4}-\d{4})',
            r'电话[：:]\s*(\d{3}-\d{4}-\d{4})',
            r'(\d{3}-\d{4}-\d{4})'
        ]
        for pattern in phone_patterns:
            phone_match = re.search(pattern, original_text)
            if phone_match:
                personal_info["电话"] = phone_match.group(1)
                break

        # 提取邮箱
        email_match = re.search(r'邮箱[：:]\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', original_text)
        if email_match:
            personal_info["邮箱"] = email_match.group(1)

        return personal_info
    
    @staticmethod
    def parse_work_experience(original_text: str) -> List[Dict[str, Any]]:
        """解析工作经历"""
        work_experiences = []
        
        # 查找所有以 # 开头的公司部分
        company_sections = re.findall(r'# ([^#\n]+)\s*\n([\s\S]*?)(?=# [^#]|$)', original_text)
        
        for company_name, content in company_sections:
            company_name = company_name.strip()
            
            # 跳过非工作经历的部分
            skip_keywords = ['大学', '项目', '技能', '教育', '荣誉', '基本信息', '自我评价', '重点项目', '联系方式']
            if any(keyword in company_name for keyword in skip_keywords):
                continue
            
            # 提取工作描述并清理特殊符号
            description = ResumeParser.clean_special_symbols(content)
            
            # 分割描述为列表项 - 关键处理逻辑
            description_items = []
            if description:
                # 先在列表符号前添加换行符
                description_with_breaks = re.sub(r'([^•●·\n])([•●·])', r'\1\n\2', description)
                
                # 按换行符分割
                items = description_with_breaks.split('\n')
                
                for item in items:
                    item = item.strip()
                    if item:
                        # 移除开头的列表符号
                        item = re.sub(r'^[•●·]\s*', '', item)
                        item = item.strip()
                        if item:
                            description_items.append(item)
            
            # 推断职位和时间（可根据需要扩展）
            position = "职位"
            start_date = ""
            end_date = ""
            
            if "阿里巴巴" in company_name:
                position = "高级项目经理"
                start_date = "2019.07"
                end_date = "至今"
            elif "腾讯" in company_name:
                position = "项目经理"
                start_date = "2017.07"
                end_date = "2019.06"
            
            if description_items:
                work_experiences.append({
                    "公司": company_name,
                    "职位": position,
                    "开始时间": start_date,
                    "结束时间": end_date,
                    "工作内容": description_items
                })
        
        return work_experiences
    
    @staticmethod
    def parse_project_experience(original_text: str) -> List[Dict[str, Any]]:
        """解析项目经验"""
        projects = []
        
        # 查找项目部分
        project_sections = re.findall(r'# ([^#\n]*?(?:系统|平台|项目))\s*\n([\s\S]*?)(?=# [^#项]|$)', original_text)
        
        for project_name, content in project_sections:
            project_name = project_name.strip()
            
            # 跳过非项目的部分
            if any(keyword in project_name for keyword in ['工作经历', '教育背景', '专业技能', '基本信息']):
                continue
            
            # 提取项目信息并清理特殊符号
            background_match = re.search(r'# 项目背景[：:]\s*\n([^#]+)', content)
            background = ResumeParser.clean_special_symbols(background_match.group(1)) if background_match else ""
            
            responsibilities_match = re.search(r'# 项目职责[：:]\s*\n([^#]+)', content)
            responsibilities_text = ResumeParser.clean_special_symbols(responsibilities_match.group(1)) if responsibilities_match else ""
            
            achievements_match = re.search(r'# 项目成果[：:]\s*\n([^#]+)', content)
            achievements_text = ResumeParser.clean_special_symbols(achievements_match.group(1)) if achievements_match else ""
            
            # 分割职责和成果为列表
            responsibilities = []
            if responsibilities_text:
                # 处理列表项
                responsibilities_with_breaks = re.sub(r'([^•●·\n])([•●·])', r'\1\n\2', responsibilities_text)
                items = responsibilities_with_breaks.split('\n')
                for item in items:
                    item = item.strip()
                    if item:
                        item = re.sub(r'^[•●·]\s*', '', item)
                        item = item.strip()
                        if item:
                            responsibilities.append(item)
            
            achievements = []
            if achievements_text:
                # 处理列表项
                achievements_with_breaks = re.sub(r'([^•●·\n])([•●·])', r'\1\n\2', achievements_text)
                items = achievements_with_breaks.split('\n')
                for item in items:
                    item = item.strip()
                    if item:
                        item = re.sub(r'^[•●·]\s*', '', item)
                        item = item.strip()
                        if item:
                            achievements.append(item)
            
            # 提取角色和公司信息
            role = ""
            company = ""
            start_date = ""
            end_date = ""
            
            # 查找角色信息
            role_match = re.search(r'(项目负责人|核心项目经理|技术负责人|产品经理)', content)
            if role_match:
                role = role_match.group(1)
            
            # 查找公司信息
            company_match = re.search(r'(阿里巴巴|腾讯|百度|字节跳动|美团|滴滴)', content)
            if company_match:
                company = company_match.group(1)
                if "阿里巴巴" in company:
                    company = "阿里巴巴集团"
                elif "腾讯" in company:
                    company = "腾讯科技"
            
            # 查找时间信息
            date_match = re.search(r'(\d{4}\.\d{2})-(\d{4}\.\d{2})', content)
            if date_match:
                start_date = date_match.group(1)
                end_date = date_match.group(2)
            
            if background or responsibilities or achievements:
                projects.append({
                    "项目名称": project_name,
                    "角色": role,
                    "公司": company,
                    "开始时间": start_date,
                    "结束时间": end_date,
                    "项目背景": background,
                    "项目职责": responsibilities,
                    "项目成果": achievements
                })
        
        return projects
    
    @staticmethod
    def parse_education(original_text: str) -> List[Dict[str, str]]:
        """解析教育背景"""
        education = []
        
        # 查找大学部分
        university_sections = re.findall(r'# ([^#\n]*大学)\s*\n([\s\S]*?)(?=# [^#]|$)', original_text)
        
        for school_name, content in university_sections:
            school_name = school_name.strip()
            
            # 提取时间信息
            date_match = re.search(r'(\d{4}\.\d{2})\s*-\s*(\d{4}\.\d{2})', content)
            start_date = date_match.group(1) if date_match else ""
            end_date = date_match.group(2) if date_match else ""
            
            # 提取专业和学历信息
            degree_match = re.search(r'# ([^#\n]+)[／/]([^#\n]+)', content)
            if degree_match:
                major = degree_match.group(1).strip()
                degree = degree_match.group(2).strip()
            else:
                major = ""
                degree = ""
            
            education.append({
                "学校": school_name,
                "学历": degree,
                "专业": major,
                "开始时间": start_date,
                "结束时间": end_date
            })
        
        return education
    
    @staticmethod
    def parse_skills(original_text: str) -> List[str]:
        """解析技能"""
        # 简单的技能提取，可以根据需要扩展
        skills = ["项目管理", "团队领导", "数据分析", "产品规划"]
        return skills
    
    @staticmethod
    def parse_resume(original_text: str) -> Dict[str, Any]:
        """解析完整简历"""
        if not original_text:
            return {
                "基本信息": {},
                "工作经历": [],
                "项目经历": [],
                "教育经历": [],
                "技能": []
            }
        
        # 解析各个部分
        personal_info = ResumeParser.parse_personal_info(original_text)
        work_experience = ResumeParser.parse_work_experience(original_text)
        project_experience = ResumeParser.parse_project_experience(original_text)
        education = ResumeParser.parse_education(original_text)
        skills = ResumeParser.parse_skills(original_text)
        
        return {
            "基本信息": personal_info,
            "工作经历": work_experience,
            "项目经历": project_experience,
            "教育经历": education,
            "技能": skills
        }
