"""
简历服务
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException, status, UploadFile
from loguru import logger

from app.models.resume import Resume, ResumeSection
from app.models.user import User
from app.schemas.resume import ResumeCreate, ResumeUpdate
from app.utils.file_handler import FileHandler
from app.services.ai_service import AIService


class ResumeService:
    """简历服务类"""
    
    def __init__(self):
        self.ai_service = AIService()
    
    @staticmethod
    def get_resume_by_id(db: Session, resume_id: int, user_id: int) -> Optional[Resume]:
        """根据ID获取简历"""
        return db.query(Resume).filter(
            Resume.id == resume_id,
            Resume.owner_id == user_id
        ).first()
    
    @staticmethod
    def get_user_resumes(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[Resume]:
        """获取用户的简历列表"""
        return db.query(Resume).filter(
            Resume.owner_id == user_id
        ).offset(skip).limit(limit).all()
    
    @staticmethod
    def create_resume(db: Session, resume_create: ResumeCreate, user_id: int) -> Resume:
        """创建简历"""
        db_resume = Resume(
            title=resume_create.title,
            candidate_name=resume_create.candidate_name,
            candidate_email=resume_create.candidate_email,
            candidate_phone=resume_create.candidate_phone,
            candidate_location=resume_create.candidate_location,
            job_intention=resume_create.job_intention,
            owner_id=user_id
        )
        
        db.add(db_resume)
        db.commit()
        db.refresh(db_resume)
        
        # 更新用户简历数量
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            user.total_resumes += 1
            db.commit()
        
        return db_resume
    
    async def upload_resume(
        self,
        db: Session,
        file: UploadFile,
        user_id: int,
        title: Optional[str] = None
    ) -> Resume:
        """上传简历文件"""
        print(f"🔍 后端DEBUG: 参数 - 文件名: {file.filename}, 用户ID: {user_id}, 标题: {title}")

        # 保存文件
        from app.utils.file_handler import FileHandler  # 确保导入
        file_path, file_type = FileHandler.save_uploaded_file(file, user_id)
        print(f"🔍 后端DEBUG: 文件保存成功 - 路径: {file_path}, 类型: {file_type}")
        
        # 创建简历记录
        resume_title = title or file.filename.rsplit('.', 1)[0]

        db_resume = Resume(
            title=resume_title,
            original_filename=file.filename,
            file_path=file_path,
            file_type=file_type,
            file_size=file.size,
            owner_id=user_id
        )
        db.add(db_resume)
        db.commit()
        db.refresh(db_resume)
        print(f"🔍 后端DEBUG: 数据库记录创建成功 - ID: {db_resume.id}")
        
        # 更新用户简历数量
        print(f"🔍 后端DEBUG: 更新用户简历数量...")
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            user.total_resumes += 1
            db.commit()
            print(f"🔍 后端DEBUG: 用户简历数量已更新: {user.total_resumes}")

        # 只提取文本内容，不进行AI解析
        try:
            print(f"🔍 后端DEBUG: 开始提取文本内容...")
            from app.utils.file_handler import FileHandler
            original_text = FileHandler.extract_text_from_file(db_resume.file_path, db_resume.file_type)
            if original_text:
                db_resume.original_text = original_text
                db.commit()
                print(f"🔍 后端DEBUG: 文本提取成功，长度: {len(original_text)}")
            else:
                print(f"🔍 后端DEBUG: 文本提取结果为空")
        except Exception as e:
            print(f"🔍 后端DEBUG: 文本提取失败: {e}")
            # 文本提取失败不影响上传

        print(f"🔍 后端DEBUG: ResumeService.upload_resume 完成，返回resume ID: {db_resume.id}")
        return db_resume

    async def _parse_and_structure_resume(self, db: Session, resume: Resume) -> None:
        """解析并结构化简历内容"""
        try:
            # 提取文本内容
            from app.utils.file_handler import FileHandler
            from app.utils.resume_parser import ResumeParser

            original_text = FileHandler.extract_text_from_file(resume.file_path, resume.file_type)

            if not original_text:
                print("🔍 后端DEBUG: 无法提取文本内容")
                return

            print(f"🔍 后端DEBUG: 开始使用新解析器解析简历...")
            print(f"🔍 后端DEBUG: 原始文本长度: {len(original_text)} 字符")
            print(f"🔍 后端DEBUG: 原始文本前200字符: {original_text[:200]}...")

            # 使用新的简历解析器
            structured_data = ResumeParser.parse_resume(original_text)

            print(f"🔍 后端DEBUG: 解析完成，结构化数据: {structured_data}")
            print(f"🔍 后端DEBUG: 工作经历数量: {len(structured_data.get('工作经历', []))}")
            print(f"🔍 后端DEBUG: 项目经验数量: {len(structured_data.get('项目经历', []))}")
            print(f"🔍 后端DEBUG: 基本信息: {structured_data.get('基本信息', {})}")

            # 更新简历数据
            resume.original_text = original_text
            resume.structured_data = structured_data
            resume.is_parsed = True
            resume.parse_error = None

            # 不再提取基本信息到顶级字段，直接使用structured_data

            db.commit()

            print(f"🔍 后端DEBUG: 最终保存的简历数据:")
            print(f"  - is_parsed: {resume.is_parsed}")
            print(f"  - structured_data: {resume.structured_data}")
            print(f"  - parse_error: {resume.parse_error}")

        except Exception as e:
            print(f"🔍 后端DEBUG: 解析失败: {e}")
            resume.parse_error = str(e)
            db.commit()
            raise




    async def parse_resume(self, db: Session, resume_id: int, user_id: int) -> Resume:
        """解析简历"""
        resume = ResumeService.get_resume_by_id(db, resume_id, user_id)
        if not resume:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="简历不存在"
            )
        
        if resume.is_parsed:
            return resume
        
        try:
            # 使用AI服务的文件解析功能（包含Python库提取文本 + AI处理）
            if resume.file_path:
                parse_result = await self.ai_service.parse_resume_file(
                    resume.file_path,
                    resume.file_type
                )

                # 提取结果
                original_text = parse_result.get("original_text", "")
                structured_data = parse_result.get("structured_data", {})
                file_info = parse_result.get("file_info", {})

                logger.info(f"文件解析完成，文本长度: {file_info.get('text_length', 0)} 字符")

            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="简历文件不存在"
                )

            # 更新简历数据
            resume.structured_data = structured_data
            resume.original_text = original_text  # 保存原始文本
            resume.is_parsed = True
            resume.parse_error = None
            
            # 提取基本信息
            basic_info = structured_data.get("基本信息", {})
            resume.candidate_name = basic_info.get("姓名")
            resume.candidate_email = basic_info.get("邮箱")
            resume.candidate_phone = basic_info.get("电话")
            resume.candidate_location = basic_info.get("居住地")
            resume.job_intention = basic_info.get("求职意向")
            
            db.commit()
            db.refresh(resume)
            
            return resume
            
        except Exception as e:
            # 记录解析错误
            resume.parse_error = str(e)
            db.commit()
            raise e
    
    async def parse_resume_text(self, db: Session, resume_text: str, user_id: int) -> Dict[str, Any]:
        """解析简历文本"""
        try:
            structured_data = await self.ai_service.parse_resume(resume_text)
            print("structured_data数据是：", structured_data)
            return {
                "success": True,
                "message": "解析成功",
                "structured_data": structured_data
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"解析失败: {str(e)}",
                "structured_data": None
            }
    
    @staticmethod
    def update_resume(
        db: Session, 
        resume_id: int, 
        user_id: int, 
        resume_update: ResumeUpdate
    ) -> Resume:
        """更新简历"""
        resume = ResumeService.get_resume_by_id(db, resume_id, user_id)
        if not resume:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="简历不存在"
            )
        
        # 更新字段
        update_data = resume_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(resume, field, value)
        
        db.commit()
        db.refresh(resume)
        
        return resume
    
    @staticmethod
    def delete_resume(db: Session, resume_id: int, user_id: int) -> bool:
        """删除简历"""
        resume = ResumeService.get_resume_by_id(db, resume_id, user_id)
        if not resume:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="简历不存在"
            )
        
        # 删除文件
        if resume.file_path:
            from app.utils.file_handler import FileHandler  # 确保导入
            FileHandler.delete_file(resume.file_path)
        
        # 删除数据库记录
        db.delete(resume)
        db.commit()
        
        # 更新用户简历数量
        user = db.query(User).filter(User.id == user_id).first()
        if user and user.total_resumes > 0:
            user.total_resumes -= 1
            db.commit()
        
        return True
    
    @staticmethod
    def create_resume_version(
        db: Session, 
        resume_id: int, 
        user_id: int, 
        title: str,
        structured_data: Dict[str, Any]
    ) -> Resume:
        """创建简历版本"""
        original_resume = ResumeService.get_resume_by_id(db, resume_id, user_id)
        if not original_resume:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="原始简历不存在"
            )
        
        # 将当前版本设为非当前版本
        original_resume.is_current = False
        
        # 创建新版本
        new_version = Resume(
            title=title,
            structured_data=structured_data,
            candidate_name=structured_data.get("基本信息", {}).get("姓名"),
            candidate_email=structured_data.get("基本信息", {}).get("邮箱"),
            candidate_phone=structured_data.get("基本信息", {}).get("电话"),
            candidate_location=structured_data.get("基本信息", {}).get("居住地"),
            job_intention=structured_data.get("基本信息", {}).get("求职意向"),
            is_parsed=True,
            version=original_resume.version + 1,
            is_current=True,
            parent_resume_id=original_resume.id,
            owner_id=user_id
        )
        
        db.add(new_version)
        db.commit()
        db.refresh(new_version)
        
        return new_version
    
    @staticmethod
    def get_resume_versions(db: Session, resume_id: int, user_id: int) -> List[Resume]:
        """获取简历版本列表"""
        # 获取原始简历
        original_resume = ResumeService.get_resume_by_id(db, resume_id, user_id)
        if not original_resume:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="简历不存在"
            )
        
        # 获取所有版本（包括原始版本和子版本）
        versions = db.query(Resume).filter(
            (Resume.id == resume_id) | (Resume.parent_resume_id == resume_id),
            Resume.owner_id == user_id
        ).order_by(Resume.version.desc()).all()
        
        return versions
