#!/usr/bin/env python3
"""
修复数据库中简历数据的字段名，统一为中文
"""

import sys
import os
import sqlite3
import json

# 添加后端路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'resume-ai-backend'))

from app.utils.resume_parser import ResumeParser

def fix_resume_data():
    """修复数据库中的简历数据"""
    db_path = "resume-ai-backend/resumeai.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 获取所有简历记录
        cursor.execute("SELECT id, original_text, structured_data, is_parsed FROM resumes")
        resumes = cursor.fetchall()
        
        print(f"📋 找到 {len(resumes)} 条简历记录")
        
        updated_count = 0
        
        for resume_id, original_text, structured_data_json, is_parsed in resumes:
            print(f"\n🔍 处理简历 #{resume_id}...")
            
            # 如果有原始文本，重新解析
            if original_text:
                print(f"  - 使用修复后的解析器重新解析...")
                try:
                    # 使用修复后的解析器
                    new_structured_data = ResumeParser.parse_resume(original_text)
                    
                    # 提取基本信息
                    basic_info = new_structured_data.get('基本信息', {})
                    candidate_name = basic_info.get('姓名')
                    candidate_email = basic_info.get('邮箱')
                    candidate_phone = basic_info.get('电话')
                    candidate_location = basic_info.get('居住地')
                    job_intention = basic_info.get('求职意向')
                    
                    # 更新数据库
                    cursor.execute("""
                        UPDATE resumes 
                        SET structured_data = ?, 
                            candidate_name = ?, 
                            candidate_email = ?, 
                            candidate_phone = ?, 
                            candidate_location = ?, 
                            job_intention = ?,
                            is_parsed = 1
                        WHERE id = ?
                    """, (
                        json.dumps(new_structured_data, ensure_ascii=False),
                        candidate_name,
                        candidate_email, 
                        candidate_phone,
                        candidate_location,
                        job_intention,
                        resume_id
                    ))
                    
                    print(f"  ✅ 更新成功:")
                    print(f"    - 姓名: {candidate_name}")
                    print(f"    - 邮箱: {candidate_email}")
                    print(f"    - 电话: {candidate_phone}")
                    print(f"    - 工作经历: {len(new_structured_data.get('工作经历', []))} 条")
                    print(f"    - 教育经历: {len(new_structured_data.get('教育经历', []))} 条")
                    
                    updated_count += 1
                    
                except Exception as e:
                    print(f"  ❌ 解析失败: {e}")
                    continue
            else:
                print(f"  - 跳过（无原始文本）")
        
        # 提交更改
        conn.commit()
        print(f"\n🎉 修复完成！共更新了 {updated_count} 条简历记录")
        
        # 验证修复结果
        print(f"\n🔍 验证修复结果...")
        cursor.execute("SELECT id, candidate_name, candidate_email, structured_data FROM resumes WHERE is_parsed = 1")
        updated_resumes = cursor.fetchall()
        
        for resume_id, name, email, structured_data_json in updated_resumes:
            if structured_data_json:
                structured_data = json.loads(structured_data_json)
                basic_info = structured_data.get('基本信息', {})
                print(f"  简历 #{resume_id}: {name} ({email}) - 字段: {list(basic_info.keys())}")
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_resume_data()
