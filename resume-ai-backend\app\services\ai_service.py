"""
AI服务 - 集成OpenRouter API
"""

import json
import httpx
from typing import Dict, Any, Optional, List
from fastapi import HTT<PERSON>Exception, status
from loguru import logger

from app.core.config import settings
from app.utils.file_handler import FileHandler


class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.base_url = settings.OPENROUTER_BASE_URL
        self.api_key = settings.OPENROUTER_API_KEY
        self.default_model = settings.DEFAULT_MODEL
        
        if not self.api_key:
            logger.warning("OpenRouter API key not configured")
    
    async def _make_request(self, messages: List[Dict[str, str]], model: str = None) -> str:
        """发送请求到OpenRouter API"""
        if not self.api_key:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="AI服务未配置"
            )
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://resumeai.com",
            "X-Title": "Resume.AI"
        }
        
        payload = {
            "model": model or self.default_model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        try:
            async with httpx.AsyncClient(timeout=settings.AI_REQUEST_TIMEOUT) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )
                
                if response.status_code != 200:
                    logger.error(f"OpenRouter API error: {response.status_code} - {response.text}")
                    raise HTTPException(
                        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                        detail="AI服务暂时不可用"
                    )
                
                result = response.json()
                return result["choices"][0]["message"]["content"]
                
        except httpx.TimeoutException:
            raise HTTPException(
                status_code=status.HTTP_504_GATEWAY_TIMEOUT,
                detail="AI服务响应超时"
            )
        except Exception as e:
            logger.error(f"AI service error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="AI服务出现错误"
            )
    
    async def parse_resume(self, resume_text: str) -> Dict[str, Any]:
        """解析简历文本"""

        # 如果没有配置API密钥，使用智能模拟解析
        if not self.api_key:
            logger.info("使用智能模拟解析模式")
            return self._mock_parse_resume(resume_text)

        system_prompt = """你是一个专业的简历解析助手。请将以下简历文本解析为结构化的JSON格式。

请按照以下格式返回JSON数据：
{
    "基本信息": {
        "姓名": "",
        "邮箱": "",
        "电话": "",
        "居住地": "",
        "求职意向": ""
    },
    "教育经历": [
        {
            "学校": "",
            "学历": "",
            "专业": "",
            "时间": "",
            "成绩": ""
        }
    ],
    "工作经历": [
        {
            "公司": "",
            "职位": "",
            "时间": "",
            "工作内容": "",
            "工作成果": ""
        }
    ],
    "项目经历": [
        {
            "项目名称": "",
            "角色": "",
            "技术栈": [],
            "时间": "",
            "描述": "",
            "成果": ""
        }
    ],
    "技能": [],
    "证书与奖项": [
        {
            "名称": "",
            "时间": "",
            "颁发机构": ""
        }
    ],
    "语言能力": []
}

重要要求：
1. 只返回纯JSON格式，不要使用```json代码块包裹
2. 不要包含任何解释文字或其他内容
3. 确保JSON格式正确且完整
4. 直接以{开始，以}结束"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请解析以下简历：\n\n{resume_text}"}
        ]

        try:
            response = await self._make_request(messages)
            # 清理响应中的代码块标记
            cleaned_response = self._clean_json_response(response)
            # 尝试解析JSON
            structured_data = json.loads(cleaned_response)
            # 标准化字段名为中文
            standardized_data = self._standardize_field_names(structured_data)
            return standardized_data
        except json.JSONDecodeError as e:
            # 如果JSON解析失败，返回错误
            logger.error(f"Failed to parse AI response as JSON: {response}")
            logger.error(f"JSON decode error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="简历解析失败，请重试"
            )

    def _clean_json_response(self, response: str) -> str:
        """清理AI响应中的代码块标记和多余内容"""
        import re

        # 移除代码块标记
        # 匹配 ```json ... ``` 或 ``` ... ``` 格式
        code_block_pattern = r'```(?:json)?\s*(.*?)\s*```'
        match = re.search(code_block_pattern, response, re.DOTALL)

        if match:
            # 如果找到代码块，提取其中的内容
            cleaned = match.group(1).strip()
            logger.info("✅ 从代码块中提取JSON内容")
        else:
            # 如果没有代码块，直接使用原始响应
            cleaned = response.strip()
            logger.info("✅ 使用原始响应内容")

        # 移除可能的前后缀文本
        # 查找第一个 { 和最后一个 }
        start_idx = cleaned.find('{')
        end_idx = cleaned.rfind('}')

        if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
            cleaned = cleaned[start_idx:end_idx + 1]
            logger.info("✅ 提取JSON对象部分")

        return cleaned

    def _standardize_field_names(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化字段名为中文"""
        if not isinstance(data, dict):
            return data

        # 字段名映射表：英文 -> 中文
        field_mapping = {
            # 基本信息字段
            "name": "姓名",
            "email": "邮箱",
            "phone": "电话",
            "location": "居住地",
            "address": "居住地",
            "job_intention": "求职意向",
            "objective": "求职意向",

            # 工作经历字段
            "company": "公司",
            "position": "职位",
            "job_title": "职位",
            "start_date": "开始时间",
            "end_date": "结束时间",
            "duration": "时间",
            "description": "工作内容",
            "responsibilities": "工作内容",
            "achievements": "工作成果",

            # 教育经历字段
            "school": "学校",
            "university": "学校",
            "degree": "学历",
            "major": "专业",
            "gpa": "成绩",
            "grade": "成绩",

            # 项目经历字段
            "project_name": "项目名称",
            "role": "角色",
            "tech_stack": "技术栈",
            "technologies": "技术栈",
            "results": "成果",

            # 其他字段
            "skills": "技能",
            "certificates": "证书与奖项",
            "awards": "证书与奖项",
            "languages": "语言能力"
        }

        standardized = {}

        for key, value in data.items():
            # 标准化当前层级的键名
            chinese_key = field_mapping.get(key, key)

            if isinstance(value, dict):
                # 递归处理嵌套字典
                standardized[chinese_key] = self._standardize_field_names(value)
            elif isinstance(value, list):
                # 处理列表中的字典
                standardized_list = []
                for item in value:
                    if isinstance(item, dict):
                        standardized_list.append(self._standardize_field_names(item))
                    else:
                        standardized_list.append(item)
                standardized[chinese_key] = standardized_list
            else:
                standardized[chinese_key] = value

        return standardized

    def _mock_parse_resume(self, resume_text: str) -> Dict[str, Any]:
        """智能模拟简历解析 - 基于文本内容进行智能提取"""
        import re

        # 初始化结果结构
        result = {
            "基本信息": {
                "姓名": "",
                "邮箱": "",
                "电话": "",
                "居住地": "",
                "求职意向": ""
            },
            "教育经历": [],
            "工作经历": [],
            "项目经历": [],
            "技能": [],
            "证书与奖项": [],
            "语言能力": []
        }

        lines = resume_text.strip().split('\n')

        # 提取基本信息
        for line in lines[:10]:  # 通常基本信息在前几行
            line = line.strip()
            if not line:
                continue

            # 提取姓名（通常是第一行或包含中文姓名的行）
            if not result["基本信息"]["姓名"] and re.match(r'^[\u4e00-\u9fa5]{2,4}$', line):
                result["基本信息"]["姓名"] = line

            # 提取邮箱
            email_match = re.search(r'[\w\.-]+@[\w\.-]+\.\w+', line)
            if email_match:
                result["基本信息"]["邮箱"] = email_match.group()

            # 提取电话
            phone_match = re.search(r'1[3-9]\d{9}', line)
            if phone_match:
                result["基本信息"]["电话"] = phone_match.group()

            # 提取地址
            if any(city in line for city in ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安', '南京', '苏州']):
                if '地址' in line or '居住' in line or '所在地' in line:
                    result["基本信息"]["居住地"] = line.replace('地址：', '').replace('居住地：', '').strip()

        # 提取求职意向
        for line in lines:
            if '求职意向' in line or '应聘职位' in line:
                intention = line.split('：')[-1].strip()
                if intention:
                    result["基本信息"]["求职意向"] = intention
                break

        # 如果没有明确的求职意向，从第二行或职位相关行推断
        if not result["基本信息"]["求职意向"]:
            for line in lines[1:5]:  # 检查前几行
                line = line.strip()
                if any(keyword in line for keyword in ['工程师', '开发', '经理', '设计师', '分析师']):
                    result["基本信息"]["求职意向"] = line
                    break

        # 提取教育经历
        education_section = False
        for line in lines:
            line = line.strip()
            if '教育经历' in line or '教育背景' in line:
                education_section = True
                continue
            elif education_section and ('工作经历' in line or '项目经历' in line):
                education_section = False
                continue

            if education_section and line:
                # 匹配时间格式
                time_match = re.search(r'(\d{4}).*?(\d{4})', line)
                if time_match or any(keyword in line for keyword in ['大学', '学院', '本科', '硕士', '博士']):
                    edu_item = {
                        "学校": "",
                        "学历": "",
                        "专业": "",
                        "时间": "",
                        "成绩": ""
                    }

                    # 提取学校
                    for school_keyword in ['大学', '学院', '学校']:
                        if school_keyword in line:
                            school_match = re.search(r'[\u4e00-\u9fa5]+' + school_keyword, line)
                            if school_match:
                                edu_item["学校"] = school_match.group()
                                break

                    # 提取学历
                    for degree in ['博士', '硕士', '本科', '专科']:
                        if degree in line:
                            edu_item["学历"] = degree
                            break

                    # 提取专业
                    if '专业' in line:
                        major_match = re.search(r'专业[：:]?\s*([^\s]+)', line)
                        if major_match:
                            edu_item["专业"] = major_match.group(1)

                    # 提取时间
                    if time_match:
                        edu_item["时间"] = f"{time_match.group(1)}-{time_match.group(2)}"

                    if edu_item["学校"] or edu_item["专业"]:
                        result["教育经历"].append(edu_item)

        # 提取工作经历
        work_section = False
        current_work = None
        for i, line in enumerate(lines):
            line = line.strip()
            if '工作经历' in line:
                work_section = True
                continue
            elif work_section and ('项目经历' in line or '技能' in line):
                work_section = False
                continue

            if work_section and line:
                # 检查是否是新的工作经历（包含时间和公司）
                time_match = re.search(r'(\d{4}).*?(\d{4})', line)
                if time_match:
                    if current_work:
                        result["工作经历"].append(current_work)

                    current_work = {
                        "公司": "",
                        "职位": "",
                        "时间": f"{time_match.group(1)}-{time_match.group(2)}",
                        "工作内容": "",
                        "工作成果": ""
                    }

                    # 提取公司名
                    company_match = re.search(r'[\u4e00-\u9fa5]+(?:公司|科技|集团|有限|技术)', line)
                    if company_match:
                        current_work["公司"] = company_match.group()

                    # 提取职位 - 检查下一行是否包含职位信息
                    if i + 1 < len(lines):
                        next_line = lines[i + 1].strip()
                        position_keywords = ['工程师', '开发', '经理', '主管', '总监', '专员', '助理', '架构师', '设计师']
                        for keyword in position_keywords:
                            if keyword in next_line:
                                pos_match = re.search(r'[\u4e00-\u9fa5]*' + keyword, next_line)
                                if pos_match:
                                    current_work["职位"] = pos_match.group()
                                    break

                    # 如果当前行就包含职位信息
                    if not current_work["职位"]:
                        position_keywords = ['工程师', '开发', '经理', '主管', '总监', '专员', '助理', '架构师', '设计师']
                        for keyword in position_keywords:
                            if keyword in line:
                                pos_match = re.search(r'[\u4e00-\u9fa5]*' + keyword, line)
                                if pos_match:
                                    current_work["职位"] = pos_match.group()
                                    break

                elif current_work and (line.startswith('-') or line.startswith('•')):
                    # 工作内容
                    content = line[1:].strip()
                    if current_work["工作内容"]:
                        current_work["工作内容"] += "; " + content
                    else:
                        current_work["工作内容"] = content

        if current_work:
            result["工作经历"].append(current_work)

        # 提取项目经历
        project_section = False
        current_project = None
        for i, line in enumerate(lines):
            line = line.strip()
            if '项目经历' in line:
                project_section = True
                continue
            elif project_section and ('技能' in line or '证书' in line):
                project_section = False
                continue

            if project_section and line:
                # 检查是否是新的项目（通常项目名在单独一行或包含时间）
                time_match = re.search(r'\((\d{4}).*?(\d{4})\)', line)
                if time_match or (not line.startswith('-') and not line.startswith('•') and
                                '项目' in line or '系统' in line or '平台' in line):
                    if current_project:
                        result["项目经历"].append(current_project)

                    current_project = {
                        "项目名称": "",
                        "角色": "",
                        "技术栈": [],
                        "时间": "",
                        "描述": "",
                        "成果": ""
                    }

                    # 提取项目名称
                    if time_match:
                        current_project["项目名称"] = line.replace(time_match.group(), '').strip()
                        current_project["时间"] = f"{time_match.group(1)}-{time_match.group(2)}"
                    else:
                        current_project["项目名称"] = line

                    # 检查后续行的项目信息
                    for j in range(i + 1, min(i + 6, len(lines))):
                        next_line = lines[j].strip()
                        if not next_line or next_line.startswith('项目'):
                            break

                        if '角色' in next_line or '职责' in next_line:
                            current_project["角色"] = next_line.split('：')[-1].strip()
                        elif '技术栈' in next_line or '技术' in next_line:
                            tech_text = next_line.split('：')[-1].strip()
                            current_project["技术栈"] = [tech.strip() for tech in re.split(r'[,，、]', tech_text) if tech.strip()]
                        elif '描述' in next_line:
                            current_project["描述"] = next_line.split('：')[-1].strip()

                elif current_project and (line.startswith('-') or line.startswith('•')):
                    # 项目描述或成果
                    content = line[1:].strip()
                    if '成果' in content or '效果' in content or '提升' in content:
                        if current_project["成果"]:
                            current_project["成果"] += "; " + content
                        else:
                            current_project["成果"] = content
                    else:
                        if current_project["描述"]:
                            current_project["描述"] += "; " + content
                        else:
                            current_project["描述"] = content

        if current_project:
            result["项目经历"].append(current_project)

        # 提取技能
        skill_section = False
        for line in lines:
            line = line.strip()
            if '技能' in line and ('清单' in line or '能力' in line or line == '技能清单:' or line == '技能:'):
                skill_section = True
                continue
            elif skill_section and ('证书' in line or '奖项' in line or '语言' in line):
                skill_section = False
                continue

            if skill_section and line:
                # 提取技能列表
                if '：' in line or ':' in line:
                    skills_text = line.split('：')[-1] if '：' in line else line.split(':')[-1]
                    skills = [skill.strip() for skill in re.split(r'[,，、\s]', skills_text) if skill.strip()]
                    result["技能"].extend(skills)
                else:
                    # 直接按分隔符分割
                    skills = [skill.strip() for skill in re.split(r'[,，、\s]', line) if skill.strip()]
                    result["技能"].extend(skills)

        # 提取证书与奖项
        cert_section = False
        for line in lines:
            line = line.strip()
            if '证书' in line or '奖项' in line:
                cert_section = True
                continue
            elif cert_section and '语言' in line:
                cert_section = False
                continue

            if cert_section and line:
                cert_item = {
                    "名称": line,
                    "时间": "",
                    "颁发机构": ""
                }

                # 提取时间
                time_match = re.search(r'\d{4}年?', line)
                if time_match:
                    cert_item["时间"] = time_match.group()
                    cert_item["名称"] = line.replace(time_match.group(), '').strip()

                result["证书与奖项"].append(cert_item)

        # 提取语言能力
        lang_section = False
        for line in lines:
            line = line.strip()
            if '语言能力' in line or '语言水平' in line:
                lang_section = True
                continue

            if lang_section and line:
                if '：' in line:
                    lang_text = line.split('：')[-1]
                    result["语言能力"].append(lang_text)
                else:
                    result["语言能力"].append(line)

        return result
    
    async def parse_job_description(self, jd_text: str) -> Dict[str, Any]:
        """解析岗位JD"""
        system_prompt = """你是一个专业的岗位JD解析助手。请将以下岗位描述解析为结构化的JSON格式。

请按照以下格式返回JSON数据：
{
    "岗位名称": "",
    "部门": "",
    "职责关键词": [],
    "要求技能": [],
    "经验要求": "",
    "学历要求": "",
    "加分项": [],
    "所在城市": "",
    "公司产品关键词": []
}

请确保返回的是有效的JSON格式，不要包含任何其他文本。"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请解析以下岗位JD：\n\n{jd_text}"}
        ]
        
        try:
            response = await self._make_request(messages)
            structured_data = json.loads(response)
            return structured_data
        except json.JSONDecodeError:
            logger.error(f"Failed to parse AI response as JSON: {response}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="JD解析失败，请重试"
            )
    
    async def analyze_match(self, resume_data: Dict[str, Any], jd_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析简历与JD的匹配度"""
        system_prompt = """你是一个专业的简历匹配分析师。请分析简历与岗位JD的匹配度，并给出详细的评分和建议。

评分标准：
- 技能匹配度：0-30分
- 项目相关性：0-30分  
- 表达精准性：0-20分
- 关键词覆盖度：0-20分
- 总分：0-100分

请按照以下格式返回JSON数据：
{
    "匹配评分": {
        "技能匹配度": 0,
        "项目相关性": 0,
        "表达精准性": 0,
        "关键词覆盖度": 0,
        "总分": 0
    },
    "已匹配技能": [],
    "缺失技能": [],
    "已匹配关键词": [],
    "缺失关键词": [],
    "优势亮点": [],
    "需要改进": [],
    "优化建议": [
        {
            "模块": "",
            "建议": "",
            "优先级": 1
        }
    ]
}

请确保返回的是有效的JSON格式，不要包含任何其他文本。"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"简历数据：\n{json.dumps(resume_data, ensure_ascii=False, indent=2)}\n\n岗位JD数据：\n{json.dumps(jd_data, ensure_ascii=False, indent=2)}"}
        ]
        
        try:
            response = await self._make_request(messages)
            analysis_result = json.loads(response)
            return analysis_result
        except json.JSONDecodeError:
            logger.error(f"Failed to parse AI response as JSON: {response}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="匹配分析失败，请重试"
            )
    
    async def generate_optimization_suggestions(
        self, 
        resume_data: Dict[str, Any], 
        jd_data: Dict[str, Any], 
        match_result: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成优化建议"""
        system_prompt = """你是一个专业的简历优化顾问。基于简历数据、岗位JD和匹配分析结果，请生成具体的优化建议。

请按照以下格式返回JSON数组：
[
    {
        "模块": "项目经历",
        "建议类型": "内容优化",
        "原始内容": "",
        "建议内容": "",
        "原因": "",
        "优先级": 1
    }
]

优先级：1-5，1为最高优先级
模块可以是：基本信息、教育经历、工作经历、项目经历、技能、证书与奖项等

请确保返回的是有效的JSON格式，不要包含任何其他文本。"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"简历数据：\n{json.dumps(resume_data, ensure_ascii=False, indent=2)}\n\n岗位JD数据：\n{json.dumps(jd_data, ensure_ascii=False, indent=2)}\n\n匹配分析结果：\n{json.dumps(match_result, ensure_ascii=False, indent=2)}"}
        ]
        
        try:
            response = await self._make_request(messages)
            suggestions = json.loads(response)
            return suggestions
        except json.JSONDecodeError:
            logger.error(f"Failed to parse AI response as JSON: {response}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="优化建议生成失败，请重试"
            )

    async def parse_resume_file(self, file_path: str, file_type: str) -> Dict[str, Any]:
        """解析简历文件 - 先用Python库提取文本，再用AI处理"""
        logger.info(f"开始解析简历文件: {file_path}, 类型: {file_type}")

        try:
            # 第一步：使用Python库提取文本内容
            logger.info("第一步：使用Python库提取文本内容")
            file_handler = FileHandler()
            extracted_text = file_handler.extract_text_from_file(file_path, file_type)

            if not extracted_text or len(extracted_text.strip()) < 10:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="文件内容为空或过短，请检查文件是否正确"
                )

            logger.info(f"文本提取成功，长度: {len(extracted_text)} 字符")
            logger.debug(f"提取的文本内容（前200字符）: {extracted_text[:200]}...")

            # 第二步：使用AI服务解析结构化数据
            logger.info("第二步：使用AI服务解析结构化数据")
            structured_data = await self.parse_resume(extracted_text)

            # 返回完整结果
            result = {
                "original_text": extracted_text,
                "structured_data": structured_data,
                "file_info": {
                    "file_path": file_path,
                    "file_type": file_type,
                    "text_length": len(extracted_text)
                }
            }

            logger.info("简历文件解析完成")
            return result

        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"简历文件解析失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"简历文件解析失败: {str(e)}"
            )
