<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试上传接口</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"], input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>测试上传接口</h1>
    
    <form id="uploadForm">
        <div class="form-group">
            <label for="fileInput">选择文件:</label>
            <input type="file" id="fileInput" accept=".pdf,.doc,.docx,.txt" required>
        </div>
        
        <div class="form-group">
            <label for="titleInput">标题 (可选):</label>
            <input type="text" id="titleInput" placeholder="输入标题...">
        </div>
        
        <div class="form-group">
            <button type="button" onclick="testSimpleUpload()" id="testBtn">测试简单上传</button>
            <button type="button" onclick="testRealUpload()" id="realBtn">测试真实上传</button>
        </div>
    </form>
    
    <div id="result" class="result" style="display: none;"></div>

    <script>
        // 获取token（从localStorage）
        const token = localStorage.getItem('token') || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzIiwiZXhwIjoxNzIyNDI2NjM0fQ.Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7E';
        
        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + (isError ? 'error' : 'success');
            resultDiv.style.display = 'block';
        }
        
        async function testSimpleUpload() {
            const fileInput = document.getElementById('fileInput');
            const titleInput = document.getElementById('titleInput');
            const testBtn = document.getElementById('testBtn');
            
            if (!fileInput.files[0]) {
                showResult('请先选择文件！', true);
                return;
            }
            
            testBtn.disabled = true;
            testBtn.textContent = '上传中...';
            
            try {
                const formData = new FormData();
                formData.append('file', fileInput.files[0]);
                if (titleInput.value) {
                    formData.append('title', titleInput.value);
                }
                
                console.log('🔍 发送测试上传请求...');
                console.log('🔍 文件:', fileInput.files[0]);
                console.log('🔍 标题:', titleInput.value);
                
                const response = await fetch('http://localhost:8001/api/v1/resume/test-upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                console.log('🔍 响应状态:', response.status);
                console.log('🔍 响应头:', response.headers);
                
                if (response.ok) {
                    const result = await response.json();
                    showResult(`✅ 测试上传成功！\n${JSON.stringify(result, null, 2)}`);
                } else {
                    const errorText = await response.text();
                    showResult(`❌ 测试上传失败 (${response.status}):\n${errorText}`, true);
                }
                
            } catch (error) {
                console.error('上传错误:', error);
                showResult(`❌ 上传错误: ${error.message}`, true);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '测试简单上传';
            }
        }
        
        async function testRealUpload() {
            const fileInput = document.getElementById('fileInput');
            const titleInput = document.getElementById('titleInput');
            const realBtn = document.getElementById('realBtn');
            
            if (!fileInput.files[0]) {
                showResult('请先选择文件！', true);
                return;
            }
            
            realBtn.disabled = true;
            realBtn.textContent = '上传中...';
            
            try {
                const formData = new FormData();
                formData.append('file', fileInput.files[0]);
                if (titleInput.value) {
                    formData.append('title', titleInput.value);
                }
                
                console.log('🔍 发送真实上传请求...');
                
                const response = await fetch('http://localhost:8001/api/v1/resume/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                console.log('🔍 响应状态:', response.status);
                
                if (response.ok) {
                    const result = await response.json();
                    showResult(`✅ 真实上传成功！\n${JSON.stringify(result, null, 2)}`);
                } else {
                    const errorText = await response.text();
                    showResult(`❌ 真实上传失败 (${response.status}):\n${errorText}`, true);
                }
                
            } catch (error) {
                console.error('上传错误:', error);
                showResult(`❌ 上传错误: ${error.message}`, true);
            } finally {
                realBtn.disabled = false;
                realBtn.textContent = '测试真实上传';
            }
        }
    </script>
</body>
</html>
