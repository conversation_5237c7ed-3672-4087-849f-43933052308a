MANIFEST.in
README.txt
RELEASE-4.5.txt
setup.cfg
setup.py
bin/pygrun
src/antlr4/BufferedTokenStream.py
src/antlr4/CommonTokenFactory.py
src/antlr4/CommonTokenStream.py
src/antlr4/FileStream.py
src/antlr4/InputStream.py
src/antlr4/IntervalSet.py
src/antlr4/LL1Analyzer.py
src/antlr4/Lexer.py
src/antlr4/ListTokenSource.py
src/antlr4/Parser.py
src/antlr4/ParserInterpreter.py
src/antlr4/ParserRuleContext.py
src/antlr4/PredictionContext.py
src/antlr4/Recognizer.py
src/antlr4/RuleContext.py
src/antlr4/StdinStream.py
src/antlr4/Token.py
src/antlr4/TokenStreamRewriter.py
src/antlr4/Utils.py
src/antlr4/__init__.py
src/antlr4/atn/ATN.py
src/antlr4/atn/ATNConfig.py
src/antlr4/atn/ATNConfigSet.py
src/antlr4/atn/ATNDeserializationOptions.py
src/antlr4/atn/ATNDeserializer.py
src/antlr4/atn/ATNSimulator.py
src/antlr4/atn/ATNState.py
src/antlr4/atn/ATNType.py
src/antlr4/atn/LexerATNSimulator.py
src/antlr4/atn/LexerAction.py
src/antlr4/atn/LexerActionExecutor.py
src/antlr4/atn/ParserATNSimulator.py
src/antlr4/atn/PredictionMode.py
src/antlr4/atn/SemanticContext.py
src/antlr4/atn/Transition.py
src/antlr4/atn/__init__.py
src/antlr4/dfa/DFA.py
src/antlr4/dfa/DFASerializer.py
src/antlr4/dfa/DFAState.py
src/antlr4/dfa/__init__.py
src/antlr4/error/DiagnosticErrorListener.py
src/antlr4/error/ErrorListener.py
src/antlr4/error/ErrorStrategy.py
src/antlr4/error/Errors.py
src/antlr4/error/__init__.py
src/antlr4/tree/Chunk.py
src/antlr4/tree/ParseTreeMatch.py
src/antlr4/tree/ParseTreePattern.py
src/antlr4/tree/ParseTreePatternMatcher.py
src/antlr4/tree/RuleTagToken.py
src/antlr4/tree/TokenTagToken.py
src/antlr4/tree/Tree.py
src/antlr4/tree/Trees.py
src/antlr4/tree/__init__.py
src/antlr4/xpath/XPath.py
src/antlr4/xpath/__init__.py
src/antlr4_python3_runtime.egg-info/PKG-INFO
src/antlr4_python3_runtime.egg-info/SOURCES.txt
src/antlr4_python3_runtime.egg-info/dependency_links.txt
src/antlr4_python3_runtime.egg-info/requires.txt
src/antlr4_python3_runtime.egg-info/top_level.txt