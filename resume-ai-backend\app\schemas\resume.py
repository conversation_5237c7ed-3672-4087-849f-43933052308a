"""
简历相关的Pydantic模型
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, validator
from datetime import datetime


class ResumeBase(BaseModel):
    """简历基础模型"""
    title: str
    candidate_name: Optional[str] = None
    candidate_email: Optional[str] = None
    candidate_phone: Optional[str] = None
    candidate_location: Optional[str] = None
    job_intention: Optional[str] = None


class ResumeCreate(ResumeBase):
    """简历创建模型"""
    pass


class ResumeUpdate(BaseModel):
    """简历更新模型"""
    title: Optional[str] = None
    candidate_name: Optional[str] = None
    candidate_email: Optional[str] = None
    candidate_phone: Optional[str] = None
    candidate_location: Optional[str] = None
    job_intention: Optional[str] = None
    structured_data: Optional[Dict[str, Any]] = None
    is_parsed: Optional[bool] = None


class ResumeUpload(BaseModel):
    """简历上传响应模型"""
    id: int
    title: Optional[str] = None  # 修复：title应该是可选的
    original_filename: str
    file_type: str
    file_size: int
    is_parsed: bool
    created_at: datetime
    original_text: Optional[str] = None  # 添加原始文本内容


class ResumeParseRequest(BaseModel):
    """简历解析请求模型"""
    resume_text: str


class ResumeParseResponse(BaseModel):
    """简历解析响应模型"""
    success: bool
    message: str
    structured_data: Optional[Dict[str, Any]] = None


class StructuredResumeData(BaseModel):
    """结构化简历数据模型"""
    基本信息: Dict[str, str]
    教育经历: List[Dict[str, Any]]
    工作经历: List[Dict[str, Any]]
    项目经历: List[Dict[str, Any]]
    技能: List[str]
    证书与奖项: List[Dict[str, Any]]
    语言能力: List[str]


class ResumeInDBBase(ResumeBase):
    """数据库中的简历基础模型"""
    id: int
    original_filename: Optional[str] = None
    file_path: Optional[str] = None
    file_type: Optional[str] = None
    file_size: Optional[int] = None
    original_text: Optional[str] = None
    structured_data: Optional[Dict[str, Any]] = None
    is_parsed: bool
    parse_error: Optional[str] = None
    version: int
    is_current: bool
    owner_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Resume(ResumeInDBBase):
    """简历响应模型"""
    pass


class ResumeInDB(ResumeInDBBase):
    """数据库中的简历模型"""
    pass


class ResumeList(BaseModel):
    """简历列表模型"""
    total: int
    items: List[Resume]


class ResumeSectionBase(BaseModel):
    """简历章节基础模型"""
    section_type: str
    section_title: str
    content: Optional[Dict[str, Any]] = None
    order_index: int = 0


class ResumeSectionCreate(ResumeSectionBase):
    """简历章节创建模型"""
    resume_id: int


class ResumeSectionUpdate(BaseModel):
    """简历章节更新模型"""
    section_title: Optional[str] = None
    content: Optional[Dict[str, Any]] = None
    order_index: Optional[int] = None


class ResumeSection(ResumeSectionBase):
    """简历章节响应模型"""
    id: int
    resume_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ResumeVersionCreate(BaseModel):
    """简历版本创建模型"""
    title: str
    structured_data: Dict[str, Any]


class ResumeVersion(BaseModel):
    """简历版本模型"""
    id: int
    title: str
    version: int
    is_current: bool
    created_at: datetime

    class Config:
        from_attributes = True
