"""
Resume.AI 智能简历优化平台 - 主应用入口
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from loguru import logger

from app.core.config import settings
from app.core.database import engine, Base
from app.api.v1.api import api_router

# 创建数据库表
Base.metadata.create_all(bind=engine)

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="智能简历优化平台API",
    version="1.0.0",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc"
)

# HTTP请求日志中间件（简化版）
@app.middleware("http")
async def log_requests(request: Request, call_next):
    # 只记录上传相关的请求
    if "/resume/upload" in str(request.url):
        print(f"🔍 文件上传请求: {request.method} {request.url}", flush=True)

    response = await call_next(request)
    return response

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有来源
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 配置可信主机中间件（开发环境暂时禁用）
# if settings.BACKEND_CORS_ORIGINS:
#     app.add_middleware(
#         TrustedHostMiddleware,
#         allowed_hosts=settings.BACKEND_CORS_ORIGINS
#     )

# 🔍 测试调试输出的端点 - 必须在API路由之前注册
# @app.get("/debug-test")
# async def debug_test():
#     import sys
#     msg = "🔍 后端DEBUG: 测试端点被调用!!!"
#     print(msg, flush=True)
#     print(msg, file=sys.stderr, flush=True)
#     sys.stdout.flush()
#     sys.stderr.flush()
#     return {"message": "调试测试端点", "debug": "如果你在控制台看到调试信息，说明print正常工作"}

# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 应用启动事件
@app.on_event("startup")
async def startup_event():
    print("🔍 后端服务启动完成", flush=True)

# 422错误专门处理器
from fastapi.exceptions import RequestValidationError
from fastapi import status

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    print(f"🔍 422错误详情: {exc}", flush=True)
    print(f"🔍 请求URL: {request.url}", flush=True)
    print(f"🔍 请求方法: {request.method}", flush=True)
    print(f"🔍 错误详情: {exc.errors()}", flush=True)
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": True,
            "message": "请求参数验证失败",
            "details": exc.errors(),
            "status_code": 422
        }
    )

# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "Internal server error",
            "status_code": 500
        }
    )

# 健康检查端点
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "Resume.AI Backend",
        "version": "1.0.0"
    }

# 根路径
@app.get("/")
async def root():
    return {
        "message": "Welcome to Resume.AI API",
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8001,  # 修改为8001与run.py保持一致
        reload=True,
        log_level="trace"
    )
